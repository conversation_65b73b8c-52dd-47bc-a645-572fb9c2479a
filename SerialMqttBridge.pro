QT = core

CONFIG += c++17 cmdline

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

TARGET = SerialMqttBridge

# 包含模块
include(src/config/config.pri)
include(src/protocol/protocol.pri)
#include(src/common/common.pri)
include(src/serial/serial.pri)
include(src/mqtt/mqtt.pri)
include(src/bridge/bridge.pri)

SOURCES += \
        src/main.cpp

# 库配置
INCLUDEPATH += $$PWD/include/log
LIBS += -L$$PWD/lib -llog

# 部署配置
# 可执行文件部署
target.path = /home/<USER>
INSTALLS += target

# 配置文件部署
config_files.files = config/config.json
config_files.path = /home/<USER>/config
INSTALLS += config_files

# 依赖库部署
lib_files.files = lib/liblog.so* lib/libQt5Mqtt.so*
lib_files.path = /home/<USER>/lib
INSTALLS += lib_files

# 启动脚本部署
scripts.files = scripts/start.sh
scripts.path = /home/<USER>/scripts
INSTALLS += scripts
